import { Component, Injector, OnInit, ChangeDetectorRef } from '@angular/core';
import { finalize } from 'rxjs/operators';
import { BsModalRef, BsModalService } from 'ngx-bootstrap/modal';
import { LeadAppServicesServiceProxy, LeadDto } from '@shared/service-proxies/service-proxies';
import { CreateLeadComponent } from '../create-lead/create-lead.component';

@Component({
  selector: 'app-people-leads',
  templateUrl: './people-leads.component.html',
  styleUrls: ['./people-leads.component.css']
})
export class PeopleLeadsComponent implements OnInit {
  allPeopleLeads: any;
  currentPage = 1;
  itemsPerPage = 10;
  displayedLeads: any[] = [];
  totalPages = 1;
  isLoading = false;
  isActive: boolean | null;
  showCreateModal = false;

  constructor(
    injector: Injector,
    private _modalService: BsModalService,
    protected _cdr: ChangeDetectorRef,
    private _leadService: LeadAppServicesServiceProxy,
  ) {
    // Remove super call since the class doesn't extend anything
  }

  ngOnInit(): void {
    this.GetPeopleLeads();
  }

  GetPeopleLeads()
  {
      this.isLoading = true;
      this._leadService.getAllLeads().subscribe(res => {
        this.allPeopleLeads = res;
        this.totalPages = Math.ceil(this.allPeopleLeads.length / this.itemsPerPage);
        this.updateDisplayedLeads();
        this.isLoading = false;
        this._cdr.detectChanges();
    })
  }

  updateDisplayedLeads(): void {
    const startIndex = (this.currentPage - 1) * this.itemsPerPage;
    this.displayedLeads = this.allPeopleLeads.slice(startIndex, startIndex + this.itemsPerPage);
  }

  changePage(page: number): void {
    if (page < 1 || page > this.totalPages) return;
    this.currentPage = page;
    this.updateDisplayedLeads();
  }

  getPaginationNumbers(): number[] {
    return Array.from({ length: this.totalPages }, (_, i) => i + 1);
  }

  openCreateModal(): void {
    try {
      const createLeadDialog = this._modalService.show(CreateLeadComponent, {
        class: 'modal-lg',
      });
    } catch (error) {
      console.error('Error opening create lead modal:', error);
    }
  }

}
