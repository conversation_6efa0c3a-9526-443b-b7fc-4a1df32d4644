import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { 
  EmailsAutomationServiceProxy, 
  CampaignLeadWrapper,
  CampaignLeadsResponse 
} from '@shared/service-proxies/service-proxies';

@Component({
  selector: 'app-campaign-leads',
  templateUrl: './campaign-leads.component.html',
  styleUrl: './campaign-leads.component.css'
})
export class CampaignLeadsComponent implements OnInit {
  CampaignLeads: CampaignLeadWrapper[] = [];

  constructor(
    protected _cdr: ChangeDetectorRef,
    private _campaignService: EmailsAutomationServiceProxy,
  ) { }

  ngOnInit(): void {
    this.GetAllCampaignLeads();
  }

  GetAllCampaignLeads() {
    this._campaignService.getAllCampaignLeads().subscribe({
      next: (res: CampaignLeadsResponse[] | undefined) => {
        if (res) {
          this.CampaignLeads = res.reduce((acc: CampaignLeadWrapper[], response) => {
            if (response.data) {
              return acc.concat(response.data);
            }
            return acc;
          }, []);
          
          // Sort leads by creation date (newest first)
          this.CampaignLeads.sort((a, b) => 
            b.createdAt.valueOf() - a.createdAt.valueOf()
          );
          
          this._cdr.detectChanges();
        }
      },
      error: (err: any) => {
        console.error("API Error:", err);
      }
    });
  }
}
