<div class="container">
    <div class="header">
      <h2>Email Accounts</h2>
      <div class="actions">
        <input type="text" placeholder="Search Email Account" />
        <button class="btn primary" (click)="openModal()">Add Account(s)</button>
      </div>
    </div>
  
    <table class="email-table">
      <thead>
        <tr>
          <th>Name</th>
          <th>Email</th>
          <th>Daily Limit</th>
          <th>Warmup Enabled</th>
          <th>Warmup Reputation</th>
          <th>Type</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let account of emailAccounts">
          <td>{{ account.from_name }}</td>
          <td>{{ account.from_email }}</td>
          <td>{{ account.daily_sent_count }} / {{ account.message_per_day }}</td>
          <td>{{ getWarmupStatus(account.warmup_details?.status) }}</td>
          <td>{{ getReputationLabel(account.warmup_details?.warmup_reputation) }}</td>
          <td>{{ getIcon(account.type) }}</td>
        </tr>
      </tbody>
    </table>
  </div>
  