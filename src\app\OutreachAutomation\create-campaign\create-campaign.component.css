/* Container Styles */
.form-container {
  max-width: 800px;
  margin: 40px auto;
  padding: 20px;
  background: #ffffff;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  font-family: Arial, sans-serif;
}

/* Heading */
h2 {
  text-align: center;
  color: #5b3cc4;
  margin-bottom: 20px;
}

/* Form Grid */
.form-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

/* Form Group */
.form-group {
  flex: 1 1 45%;
  min-width: 250px;
}

.form-group label {
  display: block;
  font-weight: bold;
  margin-bottom: 6px;
  color: #333;
}

/* Input Styles */
input[type="text"],
input[type="time"],
input[type="number"],
textarea,
select[multiple] {
  width: 100%;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 6px;
  font-size: 14px;
  background-color: #fff;
  box-sizing: border-box;
}

textarea {
  resize: vertical;
}

.is-invalid {
  border: 1px solid red !important;
}

.invalid-feedback {
  color: red;
  font-size: 0.85rem;
  margin-top: 4px;
}

/* Button Styles */
button {
  display: block;
  width: 100%;
  padding: 12px;
  margin-top: 20px;
  background-color: #5b3cc4;
  color: white;
  font-size: 16px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
}

button:hover {
  background-color: #4a30a3;
}

/* Checkbox Group */
.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 6px;
  background-color: white;
}

.checkbox-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.checkbox-item input[type="checkbox"] {
  width: 18px;
  height: 18px;
  cursor: pointer;
}

.checkbox-item label {
  margin: 0;
  cursor: pointer;
  font-weight: normal;
}

/* Dropdown Styles */
.dropdown-container,
.variable-dropdown {
  position: relative;
  width: 100%;
}

.dropdown-select,
.var-toggle-btn {
  width: 300px;
  padding: 8px 12px;
  border: 1px solid #ccc;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dropdown-select i,
.var-toggle-btn i {
  font-size: 14px;
  color: #666;
}

.dropdown-menu {
  display: none;
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #ccc;
  border-radius: 4px;
  margin-top: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  transition: all 0.2s ease;
  opacity: 0;
  visibility: hidden;
}

.dropdown-menu.show {
  display: block;
  opacity: 1;
  visibility: visible;
}

.dropdown-item {
  padding: 8px 12px;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.dropdown-item:hover {
  background-color: #f5f5f5;
}

.dropdown-item input[type="checkbox"] {
  width: 18px;
  height: 18px;
}

.dropdown-item label {
  margin: 0;
  cursor: pointer;
  flex-grow: 1;
}

/* Email Preview Style */
.form-group p {
  background: #f4f4f4;
  padding: 10px;
  border-radius: 6px;
  border: 1px solid #ccc;
  font-size: 14px;
}

/* Sequence Builder Styles */
.sequence-builder {
  display: flex;
  gap: 24px;
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  margin: 20px 0;
}

.sidebar {
  width: 280px;
  border-right: 1px solid #e0e0e0;
  padding-right: 20px;
}

.email-editor {
  flex: 1;
  min-width: 0;
}

.email-steps {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 15px;
}

.step-item {
  width: 210px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #f8f9ff;
  border: 1px solid #e6e8f0;
  border-radius: 8px;
  padding: 12px 16px;
}

.step-item.active {
  border-color: #7A5FFF;
  background: #f8f7ff;
}

.step-header {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #7A5FFF;
  font-weight: 500;
}

.step-subject {
  color: #666;
  font-size: 13px;
  margin-top: 4px;
}

.remove-step-btn {
  background: none;
  border: none;
  color: #999;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s;
  margin-left: 8px;
}

.remove-step-btn:hover {
  color: #dc3545;
  background: #fff5f5;
}

.add-step-btn {
  margin-top: 8px;
  padding: 10px;
  background: #f8f9fa;
  border: 2px dashed #dee2e6;
  border-radius: 6px;
  color: #7A5FFF;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.add-step-btn:hover {
  background: #eef1f6;
  border-color: #7A5FFF;
}

.editor-header,
.editor-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.editor-toolbar {
  display: flex;
  gap: 8px;
  padding: 8px;
  background: #f5f5f5;
  border-radius: 4px;
  margin-bottom: 8px;
}

.editor-toolbar button {
  padding: 6px;
  border: none;
  background: none;
  cursor: pointer;
  border-radius: 4px;
}

.editor-toolbar button:hover {
  background: #e0e0e0;
}

.input-with-tools {
  width: 300px;
  display: flex;
  gap: 8px;
}

.input-with-tools .input {
  flex: 1;
}

.tools button {
  padding: 8px;
  border: 1px solid #e0e0e0;
  background: #fff;
  border-radius: 4px;
  cursor: pointer;
}

.remove-btn {
  color: #dc3545;
  background: none;
  border: 1px solid #dc3545;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
}

.remove-btn:hover {
  background: #dc3545;
  color: #fff;
}

/* Responsive */
@media (max-width: 600px) {
  .form-group {
    flex: 1 1 100%;
  }
}

.email-editor-container {
  display: flex;
  gap: 20px;
  margin: 20px 0;
}

.variables-sidebar {
  width: 250px;
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.variables-toggle {
  width: 350px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px;
  background: #fff;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.variables-toggle:hover {
  background: #f8f9fa;
}

.variables-list {
  width: 350px;
  margin-top: 10px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 300px;
  overflow-y: auto;
}

.variable-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px;
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  cursor: pointer;
  user-select: none;
  transition: background-color 0.2s;
}

.variable-item:hover {
  background: #e9ecef;
}

.variable-label {
  font-size: 14px;
}

.variable-code {
  font-size: 12px;
  color: #6c757d;
  background: #f8f9fa;
  padding: 2px 4px;
  border-radius: 3px;
}

.email-content {
  flex: 1;
}

.textarea {
  width: 550px;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 10px;
}

.textarea:focus {
  outline: none;
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}

/* Sequence Container Styles */
.sequence-container {
  margin-bottom: 24px;
}

.sequence-label {
  font-size: 18px;
  font-weight: 500;
  color: #333;
  margin-bottom: 16px;
}

.email-steps {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.add-email-step {
  width: 170px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px;
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  background: none;
  color: #7A5FFF;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.add-email-step:hover {
  background: #f8f7ff;
  border-color: #7A5FFF;
}
