.leads-container {
  padding: 20px;
  font-family: 'Segoe UI', sans-serif;
}

.total-count {
  color: #666;
  font-size: 14px;
}

/* Set a fixed height for the table container */
.table-container {
  max-height: 400px; /* Adjust height as needed */
  overflow-y: auto;
  overflow-x: auto;
  border: 1px solid #ddd;
}

/* Ensure the table takes full width */
.table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

/* Fix table header on scroll */
.table-header {
  position: sticky;
  top: 0;
  background-color: #f8f9fa;
  z-index: 2;
}

/* Optional: Add a shadow effect to the fixed header */
.table-header th {
  box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
}

/* Hide scrollbar but keep functionality */
.table-container::-webkit-scrollbar {
  width: 8px;
}

.table-container::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

.table-container::-webkit-scrollbar-thumb:hover {
  background: #555;
}

th, td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

a {
  color: #5a3be0;
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
  
}