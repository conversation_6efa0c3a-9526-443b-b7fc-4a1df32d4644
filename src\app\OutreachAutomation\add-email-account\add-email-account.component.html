<div class="add-email-container">
    <h2>Add Email</h2>
    <p>Read the full tutorial on setting up your <a href="#">email account here</a></p>
  
    <!-- SMTP Section -->
    <section class="section">
      <h3>SMTP Settings (sending emails)</h3>
      <div class="form-grid">
        <input type="text" placeholder="From Name" [(ngModel)]="form.from_name" name="from_name" />
        <input type="email" placeholder="From Email" [(ngModel)]="form.from_email" name="from_email" />
        <input type="text" placeholder="User Name" [(ngModel)]="form.user_name" name="user_name" />
        <input type="password" placeholder="Password" [(ngModel)]="form.password" name="password" />
        <input type="text" placeholder="SMTP Host" [(ngModel)]="form.smtp_host" name="smtp_host"/>
        <input type="number" placeholder="SMTP Port" value="465" [(ngModel)]="form.smtp_port" name="smtp_port"/>
      </div>
    </section>
  
    <!-- Sending Preferences -->
    <section class="section">
      <div class="form-grid">
        <input type="number" placeholder="Messages Per Day" [(ngModel)]="form.max_email_per_day" name="max_email_per_day" value="200" />
        <input type="number" placeholder="Minimum Time Gap (min)" [(ngModel)]="form.daily_rampup" name="daily_rampup" />
      </div>
    </section>
  
    <!-- IMAP Section -->
    <section class="section">
      <h3>IMAP Settings</h3>
      <div class="form-grid">
        <input type="text" placeholder="IMAP Host" [(ngModel)]="form.imap_host" name="imap_host" />
        <input type="number" placeholder="IMAP Port" value="993" [(ngModel)]="form.imap_port" name="imap_port" />
      </div>
    </section>
  
    <!-- Signature Editor -->
    <section class="section">
      <h3>Signature</h3>
      <textarea placeholder="Enter your email signature..." [(ngModel)]="form.signature" name="signature"></textarea>
    </section>
  
    <!-- BCC & Custom Domain -->
    <section class="section">
      <h3>BCC</h3>
      <input type="text" placeholder="BCC Email Address" [(ngModel)]="form.bcc" name="bcc" />
    </section>
  
    <!-- Save Button -->
    <div class="actions">
      
      <button (click)="onSubmit()" style="background-color: #7A5FFF; color: white; padding: 10px 20px; border: none; border-radius: 6px;">
        Save
      </button>
    </div>
  </div>
  