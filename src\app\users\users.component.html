<div [@routerTransition]>
  <section class="content-header">
    <div class="container-fluid">
      <div class="row">
        <div class="col-6">
          <h1>{{ "Users" | localize }}</h1>
        </div>
        <div class="col-6 text-right">
          <a href="javascript:;" class="btn bg-blue" (click)="createUser()">
            <i class="fa fa-plus-square"></i>
            {{ "Create" | localize }}
          </a>
        </div>
      </div>
    </div>
  </section>
  <section class="content px-2">
    <div class="container-fluid">
      <div class="card">
        <div class="card-header">
          <div class="row">
            <div class="col-md-6">&emsp;</div>
            <div class="col-md-6">
              <div class="input-group">
                <div class="input-group-prepend">
                  <button
                    type="button"
                    class="btn bg-blue"
                    (click)="getDataPage(1)"
                    >
                    <i class="fas fa-search"></i>
                  </button>
                </div>
                <input
                  type="text"
                  class="form-control"
                  name="keyword"
                  [placeholder]="'SearchWithThreeDot' | localize"
                  [(ngModel)]="keyword"
                  (keyup.enter)="getDataPage(1)"
                  />
                  <div class="input-group-append">
                    <button
                      type="button"
                      class="btn btn-default"
                      (click)="advancedFiltersVisible = !advancedFiltersVisible"
                      >
                      <i
                        class="fas"
                        [class.fa-angle-up]="advancedFiltersVisible"
                        [class.fa-angle-down]="!advancedFiltersVisible"
                      ></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>
            @if (advancedFiltersVisible) {
              <div class="card mb-0 mt-1">
                <div class="card-body">
                  <form class="form-horizontal">
                    <div class="row">
                      <div class="col-md-6">
                        <div class="form-group row mb-0">
                          <label class="col-md-3 col-form-label">
                            {{ "IsActive" | localize }}
                          </label>
                          <div class="col-md-9 pt-2">
                            <div class="custom-control custom-radio d-inline">
                              <input
                                type="radio"
                                class="custom-control-input"
                                id="isActiveAll"
                                name="isActive"
                                [(ngModel)]="isActive"
                                [value]="undefined"
                                checked
                                />
                                <label class="custom-control-label" for="isActiveAll">
                                  {{ "All" | localize }}
                                </label>
                              </div>
                              <div class="custom-control custom-radio d-inline mx-3">
                                <input
                                  type="radio"
                                  class="custom-control-input"
                                  id="isActiveActive"
                                  name="isActive"
                                  [(ngModel)]="isActive"
                                  [value]="true"
                                  />
                                  <label
                                    class="custom-control-label"
                                    for="isActiveActive"
                                    >
                                    {{ "Yes" | localize }}
                                  </label>
                                </div>
                                <div class="custom-control custom-radio d-inline">
                                  <input
                                    type="radio"
                                    class="custom-control-input"
                                    id="isActivePassive"
                                    name="isActive"
                                    [(ngModel)]="isActive"
                                    [value]="false"
                                    />
                                    <label
                                      class="custom-control-label"
                                      for="isActivePassive"
                                      >
                                      {{ "No" | localize }}
                                    </label>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </form>
                      </div>
                      <div class="card-footer">
                        <button
                          type="button"
                          class="btn bg-blue"
                          (click)="getDataPage(1)"
                          >
                          {{ "Search" | localize }}
                        </button>
                        <button
                          type="button"
                          class="btn btn-default float-right"
                          (click)="clearFilters()"
                          >
                          {{ "Clear" | localize }}
                        </button>
                      </div>
                    </div>
                  }
                </div>
                <div class="card-body">
                  <div class="table-responsive">
                    <table class="table table-striped table-bordered" [busy]="isTableLoading">
                      <thead class="bg-light">
                        <tr>
                          <th>{{ "UserName" | localize }}</th>
                          <th>{{ "FullName" | localize }}</th>
                          <th>{{ "EmailAddress" | localize }}</th>
                          <th>{{ "IsActive" | localize }}</th>
                          <th style="width: 310px;">{{ "Actions" | localize }}</th>
                        </tr>
                      </thead>
                      <tbody>
                        @for (
                          user of users
                          | paginate
                          : {
                          id: 'server',
                          itemsPerPage: pageSize,
                          currentPage: pageNumber,
                          totalItems: totalItems
                        }
                        ; track
                        user.id) {
                        <tr
                          >
                          <td>{{ user.userName }}</td>
                          <td>{{ user.fullName }}</td>
                          <td>{{ user.emailAddress }}</td>
                          <td>
                            <div class="custom-control custom-checkbox">
                              <input
                                type="checkbox"
                                class="custom-control-input"
                                disabled
                                [checked]="user.isActive"
                                />
                                <label class="custom-control-label"></label>
                              </div>
                            </td>
                            <td>
                              <button
                                type="button"
                                class="btn btn-sm bg-secondary"
                                (click)="editUser(user)"
                                >
                                <i class="fas fa-pencil-alt"></i>
                                {{ "Edit" | localize }}
                              </button>
                              <button
                                type="button"
                                class="btn btn-sm bg-danger mx-2"
                                (click)="delete(user)"
                                >
                                <i class="fas fa-trash"></i>
                                {{ "Delete" | localize }}
                              </button>
                              <button
                                type="button"
                                class="btn btn-sm bg-secondary"
                                (click)="resetPassword(user)"
                                >
                                <i class="fas fa-lock"></i>
                                {{ "ResetPassword" | localize }}
                              </button>
                            </td>
                          </tr>
                        }
                      </tbody>
                    </table>
                  </div>
                </div>
                <div class="card-footer table-card-footer bg-light border-top">
                  <div class="row">
                    <div class="col-sm-4 col-12 text-sm-left text-center">
                      <button class="btn btn-secondary" (click)="refresh()">
                        <i class="fas fa-redo-alt"></i>
                      </button>
                    </div>
                    <div class="col-sm-4 col-12 text-center">
                      <p class="mb-0 my-2">
                        {{ "TotalRecordsCount" | localize: totalItems }}
                      </p>
                    </div>
                    <div class="col-sm-4 col-12">
                      <div class="float-sm-right m-auto">
                        <abp-pagination-controls
                          id="server"
                          (pageChange)="getDataPage($event)"
                          >
                        </abp-pagination-controls>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>
        </div>
