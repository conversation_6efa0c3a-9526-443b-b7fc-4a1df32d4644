<div class="leads-container">
    <h2>All Leads <span class="total-count">({{CampaignLeads.length}})</span></h2>
    <div class="card">
      <div class="table-responsive table-container">
        <table class="table table-striped mt-2">
          <thead class="bg-light table-header">
            <tr>
              <th>Sr No.</th>
              <th>Email</th>
              <th>First name</th>
              <th>Last name</th>
              <th>Company URL</th>
              <th>Company Name</th>
              <th>Created At</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let wrapper of CampaignLeads; let i = index">
              <td>{{ i + 1 }}</td>
              <td><a href="mailto:{{ wrapper.lead.email }}">{{ wrapper.lead.email }}</a></td>
              <td>{{ wrapper.lead.firstName }}</td>
              <td>{{ wrapper.lead.lastName }}</td>
              <td><a [href]="wrapper.lead.companyUrl" target="_blank">{{ wrapper.lead.companyUrl }}</a></td>
              <td>{{ wrapper.lead.companyName }}</td>
              <td>{{ wrapper.createdAt | date:'medium' }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  
