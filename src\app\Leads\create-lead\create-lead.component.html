<div class="container mt-4 p-4">
    <h3>Scraping Leads</h3>
    <form (ngSubmit)="onSubmit()" #leadForm="ngForm">
      <div class="form-check mb-2">
        <input
          class="form-check-input"
          type="checkbox"
          id="personalEmails"
          name="getPersonalEmails"
          [(ngModel)]="leadData.getPersonalEmails"
        />
        <label class="form-check-label" for="personalEmails">
          Get Personal Emails
        </label>
      </div>
  
      <div class="form-check mb-3">
        <input
          class="form-check-input"
          type="checkbox"
          id="workEmails"
          name="getWorkEmails"
          [(ngModel)]="leadData.getWorkEmails"
        />
        <label class="form-check-label" for="workEmails">
          Get Work Emails
        </label>
      </div>
  
      <div class="mb-3">
        <label for="totalRecords" class="form-label">Total Records</label>
        <input
          type="number"
          class="form-control"
          id="totalRecords"
          name="totalRecords"
          [(ngModel)]="leadData.totalRecords"
        />
      </div>
  
      <div class="mb-3">
        <label for="url" class="form-label">Apollo URL</label>
        <input
          type="text"
          class="form-control"
          id="url"
          name="url"
          placeholder="Paste Apollo.io URL"
          [(ngModel)]="leadData.url"
        />
      </div>
  
      <button type="submit" class="btn btn-primary mr-2">Submit</button>
      <button type="button" class="btn btn-danger" aria-label="Close" (click)="closeModal()">Close</button>

    </form>
  </div>
  