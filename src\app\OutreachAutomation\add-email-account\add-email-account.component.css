/* Add Email Form Styles */
.add-email-container {
    max-width: 900px;
    margin: auto;
    padding: 1rem;
    font-family: Arial, sans-serif;
  }
  
  h2,
  h3 {
    color: #333;
  }
  
  .section {
    margin-top: 2rem;
    border-top: 1px solid #eee;
    padding-top: 1rem;
  }
  
  .form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
  }
  
  input[type="text"],
  input[type="email"],
  input[type="number"],
  input[type="password"],
  textarea,
  select {
    padding: 0.6rem;
    width: 100%;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 14px;
    box-sizing: border-box;
    margin-bottom: 1rem;
  }
  
  textarea {
    height: 120px;
    resize: vertical;
  }
  
  select {
    background-color: white;
  }
  
  .radio-group {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
  }
  
  .checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 1rem;
  }
  
  .actions {
    text-align: center;
    margin-top: 2rem;
  }
  
  .primary-button {
    background-color: #7a5fff;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    cursor: pointer;
  }
  
  .primary-button:hover {
    background-color: #684dff;
  }
  
  /* Responsive */
  @media (max-width: 600px) {
    .form-grid {
      grid-template-columns: 1fr;
    }
  
    .radio-group {
      flex-direction: column;
      align-items: flex-start;
    }
  
    .actions {
      text-align: center;
    }
  }
  