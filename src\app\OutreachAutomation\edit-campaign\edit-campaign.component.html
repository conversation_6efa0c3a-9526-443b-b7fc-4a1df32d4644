<div class="form-container">
    <h2>Edit Campaign</h2>
  
    <div class="action-buttons">
      <button class="export-btn" (click)="exportCampaignData()">
        <i class="fas fa-download"></i> Export Campaign Data
      </button>
    </div>
  
    <div class="form-grid">
      <!-- Campaign Name Field -->
      <div class="form-group">
        <label for="campaignName">Campaign Name</label>
        <input 
          id="campaignName" 
          type="text" 
          [(ngModel)]="campaignName" 
          placeholder="Enter campaign name">
        <div *ngIf="formSubmitted && !campaignName.trim()" class="invalid-feedback">
          Campaign name is required.
        </div>
      </div>
  
      <!-- Days of the Week Field -->
      <div class="form-group">
        <label for="daysOfWeek">Days of the Week</label>
        <div class="dropdown-container">
          <div class="dropdown-select" (click)="toggleDropdown()">
            <span *ngIf="selectedDays.length === 0">Select Days</span>
            <span *ngIf="selectedDays.length > 0">{{ getSelectedDaysText() }}</span>
            <i class="fas fa-chevron-down"></i>
          </div>
          <div class="dropdown-menu" [class.show]="isDropdownOpen">
            <div *ngFor="let day of daysOfWeekOptions" class="dropdown-item">
              <input 
                type="checkbox" 
                [id]="'day_' + day.value"
                [checked]="selectedDays.includes(day.value)"
                (change)="toggleDay(day.value)">
              <label [for]="'day_' + day.value">{{ day.label }}</label>
            </div>
          </div>
        </div>
        <div *ngIf="formSubmitted && selectedDays.length === 0" class="invalid-feedback">
          Please select at least one day.
        </div>
      </div>
  
      <!-- Email ID Field -->
      <div class="form-group">
        <label for="emailId">Email ID</label>
        <div class="dropdown-container">
          <div class="dropdown-select" (click)="toggleEmailDropdown()">
            <span *ngIf="!selectedEmail">Select Email</span>
            <span *ngIf="selectedEmail">{{ getSelectedEmailText() }}</span>
            <i class="fas fa-chevron-down"></i>
          </div>
          <div class="dropdown-menu" [class.show]="isEmailDropdownOpen">
            <div *ngFor="let email of emailAccounts" class="dropdown-item">
              <input 
                type="checkbox" 
                [id]="'email_' + email.id"
                [checked]="selectedEmail === email.id"
                (change)="toggleEmailSelection(email)">
              <label [for]="'email_' + email.id">{{ email.from_name }} &lt;{{ email.from_email }}&gt;</label>
            </div>
          </div>
        </div>
        <div *ngIf="formSubmitted && !selectedEmail" class="invalid-feedback">
          Please select an email.
        </div>
      </div>
  
      <!-- Start Hour Field -->
      <div class="form-group">
        <label for="startHour">Start Hour</label>
        <input 
          id="startHour" 
          type="time" 
          [(ngModel)]="startHour">
        <div *ngIf="formSubmitted && !startHour" class="invalid-feedback">
          Start hour is required.
        </div>
      </div>
  
      <!-- End Hour Field -->
      <div class="form-group">
        <label for="endHour">End Hour</label>
        <input 
          id="endHour" 
          type="time" 
          [(ngModel)]="endHour">
        <div *ngIf="formSubmitted && !endHour" class="invalid-feedback">
          End hour is required.
        </div>
      </div>
  
      <!-- Min Time Between Emails Field -->
      <div class="form-group">
        <label for="minTimeBetweenEmails">Min Time Between Emails (minutes)</label>
        <input 
          id="minTimeBetweenEmails" 
          type="number" 
          [(ngModel)]="minTimeBetweenEmails">
        <div *ngIf="formSubmitted && minTimeBetweenEmails <= 0" class="invalid-feedback">
          Must be greater than 0.
        </div>
      </div>
  
      <!-- Max New Leads Per Day Field -->
      <div class="form-group">
        <label for="maxLeadsPerDay">Max New Leads Per Day</label>
        <input 
          id="maxLeadsPerDay" 
          type="number" 
          [(ngModel)]="maxLeadsPerDay">
        <div *ngIf="formSubmitted && maxLeadsPerDay <= 0" class="invalid-feedback">
          Must be greater than 0.
        </div>
      </div>
    </div>
  
    <!-- Email Sequence Section -->
    <div class="sequence-container">
      <h3 class="sequence-label">Email Sequence</h3>
      <div class="email-steps">
        <div 
          class="step-item" 
          *ngFor="let step of emailSequence; let i = index" 
          [class.active]="i === selectedStep">
          <div class="step-content" (click)="selectStep(i)">
            <div class="step-header">
              <i class="fas fa-envelope"></i>
              <span>Email follow up {{ i + 1 }}</span>
            </div>
            <div class="step-subject">Subject: {{ step.subject || '(not set)' }}</div>
          </div>
          <button 
            class="remove-step-btn" 
            (click)="removeEmailStage(i)"
            *ngIf="emailSequence.length > 1">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <button 
          class="add-email-step" 
          (click)="addEmailStage()"
          *ngIf="emailSequence.length < 5">
          <i class="fas fa-plus"></i>
          <span>Add Email Step</span>
        </button>
      </div>
    </div>
  
    <!-- Toggle Variables Button -->
    <button class="var-toggle-btn" (click)="toggleVariables()">
      {{ showVariables ? 'Hide Variables' : 'Show Variables' }}
    </button>
  
    <!-- Variables Section (Shown Conditionally) -->
    <div class="variables-container" *ngIf="showVariables">
      <h3 class="variables-label">Variables</h3>
      <div class="variables-list">
        <div *ngFor="let variable of variables" 
             class="variable-item"
             draggable="true"
             (dragstart)="onDragStart($event, variable.code)">
          <div class="variable-content">
            <span class="equals-sign">=</span>
            <span class="variable-label">{{ variable.label }}</span>
            <code class="variable-code">{{ variable.code }}</code>
          </div>
        </div>
      </div>
    </div>
  
    <!-- Email Editor Section -->
    <div class="email-editor-container">
      <div class="email-content">
        <div class="form-group">
          <label for="emailSubject">Subject:</label>
          <div class="input-with-tools">
            <input 
              id="emailSubject"
              type="text" 
              [(ngModel)]="emailSequence[selectedStep].subject" 
              placeholder="Hi {{'{{'}}first_name{{'}}'}}" 
              class="input"
              (drop)="onDrop($event, 'subject')"
              (dragover)="onDragOver($event)">
          </div>
        </div>
  
        <div class="form-group">
          <label for="emailBody">Email Body:</label>
          <textarea 
            id="emailBody"
            rows="12" 
            [(ngModel)]="emailSequence[selectedStep].body" 
            class="textarea"
            placeholder="Write your email content here..."
            (drop)="onDrop($event, 'body')"
            (dragover)="onDragOver($event)">
          </textarea>
        </div>
      </div>
    </div>
  
    <!-- Submit Button -->
    <button (click)="save()">Update Campaign</button>
  </div>
  
