import { ChangeDetectorRef, Component, EventEmitter, Injector, OnInit, Output } from '@angular/core';
import { BsModalRef } from 'ngx-bootstrap/modal';
import { EmailsAutomationServiceProxy, SmartleadAIDto, EmailSequenceInput } from '@shared/service-proxies/service-proxies';

@Component({
  selector: 'app-edit-campaign',
  templateUrl: './edit-campaign.component.html',
  styleUrl: './edit-campaign.component.css'
})
export class EditCampaignComponent implements OnInit {
  id: string;
  campaignName = '';
  startHour = '';
  endHour = '';
  minTimeBetweenEmails = 0;
  maxLeadsPerDay = 0;
  selectedEmail: any = null;
  selectedDays: number[] = [];
  emailAccounts: any[] = [];
  formSubmitted: boolean = false;

  emailSequence: { subject: string, body: string }[] = [
    { subject: '', body: '' }
  ];

  selectedStep: number = 0;
  isVariablesMenuOpen = false;
  showVariables = false;
  private focusedElement: HTMLInputElement | HTMLTextAreaElement | null = null;

  @Output() onSave = new EventEmitter<any>();

  daysOfWeekOptions = [
    { value: 1, label: 'Monday' },
    { value: 2, label: 'Tuesday' },
    { value: 3, label: 'Wednesday' },
    { value: 4, label: 'Thursday' },
    { value: 5, label: 'Friday' },
    { value: 6, label: 'Saturday' }
  ];

  variables = [
    { label: 'Website', code: '{{website}}' },
    { label: 'Linkedin Profile', code: '{{linkedin_profile}}' },
    { label: 'Location', code: '{{location}}' },
    { label: 'Day of week', code: '{{sl_day_of_week}}' },
    { label: 'Time of day', code: '{{sl_time_of_day}}' },
    { label: 'First Name', code: '{{first_name}}' },
    { label: 'Last Name', code: '{{last_name}}' },
    { label: 'Email', code: '{{email}}' },
    { label: 'Phone Number', code: '{{phone_number}}' },
    { label: 'Company Name', code: '{{company_name}}' }
  ];

  constructor(
    injector: Injector,
    public bsModalRef: BsModalRef,
    protected _cdr: ChangeDetectorRef,
    private _emailService: EmailsAutomationServiceProxy,
  ) {}

  ngOnInit(): void {
    this.loadEmailAccounts();
    this.loadCampaignDetails();
  }

  loadEmailAccounts(): void {
    this._emailService.getEmailAccounts(0, 10).subscribe({
      next: (res) => {
        this.emailAccounts = res || [];
        this._cdr.detectChanges();
      },
      error: (err) => {
        console.error("Error fetching email accounts:", err);
      }
    });
  }

  loadCampaignDetails(): void {
    // this._emailService.getCampaignById(this.id).subscribe({
    //   next: (campaign) => {
    //     this.campaignName = campaign.name;
    //     this.selectedDays = campaign.days_of_the_week;
    //     this.startHour = campaign.start_hour;
    //     this.endHour = campaign.end_hour;
    //     this.minTimeBetweenEmails = campaign.min_time_btw_emails;
    //     this.maxLeadsPerDay = campaign.max_new_leads_per_day;
    //     this.selectedEmail = campaign.emailId;
    //     this.emailSequence = campaign.emailSequence.map(seq => ({
    //       subject: seq.subject,
    //       body: seq.body
    //     }));
    //     this._cdr.detectChanges();
    //   },
    //   error: (err) => {
    //     console.error("Error loading campaign:", err);
    //     alert('Failed to load campaign details');
    //   }
    // });
  }

  save(): void {
    this.formSubmitted = true;

    const isInvalid =
      !this.campaignName.trim() ||
      !this.selectedDays.length ||
      !this.selectedEmail ||
      !this.startHour ||
      !this.endHour ||
      this.minTimeBetweenEmails <= 0 ||
      this.maxLeadsPerDay <= 0 ||
      !this.emailSequence.some(email => email.subject.trim() && email.body.trim());

    if (isInvalid) {
      alert('Please fill in all required fields including at least one email sequence');
      return;
    }

    const validEmailSequences = this.emailSequence.filter(
      email => email.subject.trim() && email.body.trim()
    );

    // this._emailService.updateCampaign(this.id, new SmartleadAIDto({
    //   campaignName: this.campaignName,
    //   days_of_the_week: this.selectedDays,
    //   start_hour: this.startHour,
    //   end_hour: this.endHour,
    //   min_time_btw_emails: this.minTimeBetweenEmails,
    //   max_new_leads_per_day: this.maxLeadsPerDay,
    //   emailId: this.selectedEmail,
    //   emailInputs: validEmailSequences.map((email, index) => new EmailSequenceInput({
    //     sequenceNumber: index + 1,
    //     delayInDays: index * 1,
    //     subject: email.subject.trim(),
    //     body: email.body.trim(),
    //     variantLabel: `Variant ${index + 1}`
    //   }))
    // })).subscribe({
    //   next: () => {
    //     this.onSave.emit();
    //     this.bsModalRef.hide();
    //   },
    //   error: (err) => {
    //     console.error('API error:', err);
    //     alert('Failed to update campaign: ' + (err.message || 'Unknown error'));
    //   }
    // });
  }

  // Helper methods (same as CreateCampaignComponent)
  addEmailStage(): void {
    if (this.emailSequence.length < 5) {
      this.emailSequence.push({ subject: '', body: '' });
      this.selectedStep = this.emailSequence.length - 1;
      this._cdr.detectChanges();
    }
  }

  removeEmailStage(index: number): void {
    this.emailSequence.splice(index, 1);
    if (this.selectedStep >= this.emailSequence.length) {
      this.selectedStep = this.emailSequence.length - 1;
    }
    this._cdr.detectChanges();
  }

  selectStep(index: number): void {
    this.selectedStep = index;
    this._cdr.detectChanges();
  }

  insertVariable(variableCode: string): void {
    const element = this.focusedElement;
    
    if (element && (element.id === 'emailSubject' || element.id === 'emailBody')) {
      const cursorPosition = element.selectionStart || 0;
      const currentValue = element.value;
      const newValue = currentValue.slice(0, cursorPosition) + 
                      variableCode + 
                      currentValue.slice(cursorPosition);
      
      if (element.id === 'emailSubject') {
        this.emailSequence[this.selectedStep].subject = newValue;
      } else {
        this.emailSequence[this.selectedStep].body = newValue;
      }
      
      setTimeout(() => {
        element.focus();
        element.selectionStart = element.selectionEnd = cursorPosition + variableCode.length;
      }, 0);
    }
    
    this.isVariablesMenuOpen = false;
    this._cdr.detectChanges();
  }

  toggleDay(day: number): void {
    if (this.selectedDays.includes(day)) {
      this.selectedDays = this.selectedDays.filter(d => d !== day);
    } else {
      this.selectedDays.push(day);
    }
    this._cdr.detectChanges();
  }

  // Add these new properties
  isDropdownOpen = false;
  isEmailDropdownOpen = false;

  // Add these new methods
  toggleDropdown(): void {
    this.isDropdownOpen = !this.isDropdownOpen;
  }

  toggleEmailDropdown(): void {
    this.isEmailDropdownOpen = !this.isEmailDropdownOpen;
  }

  getSelectedDaysText(): string {
    if (this.selectedDays.length === 0) return 'Select Days';
    if (this.selectedDays.length === this.daysOfWeekOptions.length) return 'All Days';
    
    return this.selectedDays
      .map(value => this.daysOfWeekOptions.find(day => day.value === value)?.label)
      .filter(label => label)
      .join(', ');
  }

  getSelectedEmailText(): string {
    if (!this.selectedEmail) return 'Select Email';
    
    const email = this.emailAccounts.find(option => option.id === this.selectedEmail);
    return email ? email.from_name : 'Select Email';
  }

  toggleEmailSelection(email: any): void {
    if (this.selectedEmail === email.id) {
      this.selectedEmail = null;
    } else {
      this.selectedEmail = email.id;
    }
  }

  toggleVariables(): void {
    this.showVariables = !this.showVariables;
  }

  onDragStart(event: DragEvent, variableCode: string): void {
    if (event.dataTransfer) {
      event.dataTransfer.setData('text/plain', variableCode);
    }
  }

  onDragOver(event: DragEvent): void {
    event.preventDefault();
  }

  onDrop(event: DragEvent, field: 'subject' | 'body'): void {
    event.preventDefault();
    if (!event.dataTransfer) return;

    const variableCode = event.dataTransfer.getData('text/plain');
    if (field === 'subject') {
      this.emailSequence[this.selectedStep].subject += variableCode;
    } else {
      this.emailSequence[this.selectedStep].body += variableCode;
    }
  }

  exportCampaignData(): void {
    try {
      // Create a data object with all campaign information
      const campaignData = {
        name: this.campaignName,
        selectedDays: this.selectedDays,
        selectedEmail: this.selectedEmail,
        emailSequence: this.emailSequence,
        // Add any other relevant campaign data
      };
      
      // Convert to JSON string
      const jsonData = JSON.stringify(campaignData, null, 2);
      
      // Create a blob and download link
      const blob = new Blob([jsonData], { type: 'application/json' });
      const url = window.URL.createObjectURL(blob);
      
      // Create a temporary link element and trigger download
      const a = document.createElement('a');
      a.href = url;
      a.download = `${this.campaignName || 'campaign'}_export.json`;
      document.body.appendChild(a);
      a.click();
      
      // Clean up
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Error exporting campaign data:', error);
      // You might want to add a notification here
    }
  }
}
