.container {
    max-width: 600px;
    margin: auto;
    padding: 20px;
  }
  
  .lead-card {
    border: 1px solid #ddd;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 10px;
    background-color: #f9f9f9;
  }
  
  a {
    color: blue;
    text-decoration: none;
  }
  
  a:hover {
    text-decoration: underline;
  }

  /* Set a fixed height for the table container */
.table-container {
    max-height: 400px; /* Adjust height as needed */
    overflow-y: auto;
    overflow-x: auto;
    border: 1px solid #ddd;
  }
  
  /* Ensure the table takes full width */
  .table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
  }
  
  /* Fix table header on scroll */
  .table-header {
    position: sticky;
    top: 0;
    background-color: #f8f9fa; /* Bootstrap light gray */
    z-index: 2;
  }
  
  /* Optional: Add a shadow effect to the fixed header */
  .table-header th {
    box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
  }
  
  /* Hide scrollbar but keep functionality */
  .table-container::-webkit-scrollbar {
    width: 8px;
  }
  
  .table-container::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
  }
  
  .table-container::-webkit-scrollbar-thumb:hover {
    background: #555;
  }
  
  /* Pagination Styling */
  .pagination .page-item.active .page-link {
    background-color: #007bff;
    color: white;
    border: none;
  }
  
  .pagination .page-link {
    cursor: pointer;
  }
  
  