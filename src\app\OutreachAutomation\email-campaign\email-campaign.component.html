<div class="dashboard-container">
  <div class="header">
    <div class="title">All Campaigns</div>
    <button class="create-button" (click)="openModal()">+ Create Campaign</button>
  </div>

  <div *ngIf="!isLoading && campaigns.length === 0">
    No campaigns found.
  </div>

  <div *ngIf="!isLoading">
    <div *ngFor="let campaign of campaigns" class="campaign-card">
      <div class="progress-circle">
        <span>{{ getProgress(campaign) }}%</span>
      </div>

      <div class="campaign-info">
        <a href="#" class="campaign-name">{{ campaign.name }}</a>
        <div class="campaign-meta">
          {{ campaign.status }} | Created At: {{ campaign.created_at | date: 'medium' }} | {{ campaign.sequence_count }} sequences
        </div>
      </div>

      <div class="campaign-report">
        <div class="report-item">
          <div class="report-value">{{ campaign.campaign_lead_stats?.total }}</div>
          <div class="report-label">Leads</div>
        </div>
        <div class="report-item">
          <div class="report-value">{{ campaign.sent_count }}</div>
          <div class="report-label">Sent</div>
        </div>
        <div class="report-item">
          <div class="report-value">{{ campaign.open_count }}</div>
          <div class="report-label">Opened</div>
        </div>
        <div class="report-item">
          <div class="report-value">{{ campaign.reply_count }}</div>
          <div class="report-label">Replied</div>
        </div>
        <div class="report-item">
          <div class="report-value">{{ campaign.campaign_lead_stats?.completed }}</div>
          <div class="report-label">Completed</div>
        </div>
        <div class="report-item">
          <div class="report-value">{{ campaign.campaign_lead_stats?.inprogress }}</div>
          <div class="report-label">Inprogress</div>
        </div>
        <div class="report-item">
          <div class="report-value">{{ campaign.campaign_lead_stats?.blocked }}</div>
          <div class="report-label">Blocked</div>
        </div>
      </div>
      <div class="campaign-actions">
        <button class="inbox-button" (click)="editCampaign(campaign.id)" >Edit</button>
      </div>
       <!-- ✅ Delete Button -->
       <div class="campaign-actions">
        <button class="delete-button" (click)="deleteCampaign(campaign.id)">Delete</button>
      </div>
    </div>
  </div>
</div>
