import { Component, HostListener, OnInit } from '@angular/core';
import { ChangeDetectorRef, Injector } from '@angular/core';
import { EmailsAutomationServiceProxy, EmailSequenceInput, SmartleadAIDto } from '@shared/service-proxies/service-proxies';
import { BsModalService } from 'ngx-bootstrap/modal';

@Component({
  selector: 'app-create-campaign',
  templateUrl: './create-campaign.component.html',
  styleUrl: './create-campaign.component.css'
})
export class CreateCampaignComponent implements OnInit {

  campaignName = '';
  startHour = '';
  endHour = '';
  minTimeBetweenEmails = 0;
  maxLeadsPerDay = 0;
  daysOfWeekOptions = [
  { value: 1, label: 'Monday' },
  { value: 2, label: 'Tuesday' },
  { value: 3, label: 'Wednesday' },
  { value: 4, label: 'Thursday' },
  { value: 5, label: 'Friday' },
  { value: 6, label: 'Saturday' }
 ];

 variables = [
    { label: 'Website', code: '{{website}}' },
    { label: 'Linkedin Profile', code: '{{linkedin_profile}}' },
    { label: 'Location', code: '{{location}}' },
    { label: 'Day of week', code: '{{sl_day_of_week}}' },
    { label: 'Time of day', code: '{{sl_time_of_day}}' },
    { label: 'First Name', code: '{{first_name}}' },
    { label: 'Last Name', code: '{{last_name}}' },
    { label: 'Email', code: '{{email}}' },
    { label: 'Phone Number', code: '{{phone_number}}' },
    { label: 'Company Name', code: '{{company_name}}' }
  ];

  emailAccounts: any[] = []; 
  selectedEmail: any = null; 
  selectedDays: number[] = [];
  isDropdownOpen = false;
  isEmailDropdownOpen = false;
  formSubmitted: boolean = false;

  emailSequence: { subject: string, body: string }[] = [
    { subject: '', body: '' }
  ];

  selectedStep: number = 0;
  isVariablesMenuOpen= false;
  showVariables = false;
  private focusedElement: HTMLInputElement | HTMLTextAreaElement | null = null

   constructor(
        injector: Injector,
        private _modalService: BsModalService,
        protected _cdr: ChangeDetectorRef,
        private _emailService: EmailsAutomationServiceProxy,
      ) {
        // Remove super call since the class doesn't extend anything
      }
      
  ngOnInit(): void {
    this._emailService.getEmailAccounts(0, 10).subscribe({
      next: (res) => {
        this.emailAccounts = res || [];
        console.log("emails accounts:", this.emailAccounts);
        this._cdr.detectChanges();
      },
      error: (err) => {
        console.error("Error fetching email accounts:", err);
      }
    });
  }

  toggleDropdown() {
    this.isDropdownOpen = !this.isDropdownOpen;
  }

  toggleEmailDropdown() {
    this.isEmailDropdownOpen = !this.isEmailDropdownOpen;
  }

  getSelectedDaysText(): string {
    if (this.selectedDays.length === 0) return 'Select Days';
    if (this.selectedDays.length === this.daysOfWeekOptions.length) return 'All Days';
    
    return this.selectedDays
      .map(value => this.daysOfWeekOptions.find(day => day.value === value)?.label)
      .filter(label => label)
      .join(', ');
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: MouseEvent) {
    const dropdownElement = event.target as HTMLElement;
    if (!dropdownElement.closest('.dropdown-container')) {
      this.isDropdownOpen = false;
    }
  }

  onDaysChange(event: any) {
  console.log('Selected Day Values:', this.selectedDays); // [1, 3, 5] etc.
}

  toggleDay(day: number) {
    if (this.selectedDays.includes(day)) {
      this.selectedDays = this.selectedDays.filter(d => d !== day);
    } else {
      this.selectedDays.push(day);
    }
  }

  getSelectedEmailText(): string {
    if (!this.selectedEmail) return 'Select Email';
    
    const email = this.emailAccounts.find(option => option.id === this.selectedEmail);
    return email ? email.from_name : 'Select Email';
  }

  toggleEmailSelection(email: any) {
    if (this.selectedEmail === email.id) {
      this.selectedEmail = null;
    } else {
      this.selectedEmail = email.id;
    }
  }

  addEmailStage(): void {
    if (this.emailSequence.length < 5) {
      this.emailSequence.push({ subject: '', body: '' });
      this.selectedStep = this.emailSequence.length - 1;
      this._cdr.detectChanges();
    }
  }

  removeEmailStage(index: number): void {
    if (this.emailSequence.length > 1) {
      this.emailSequence.splice(index, 1);
      if (this.selectedStep >= this.emailSequence.length) {
        this.selectedStep = this.emailSequence.length - 1;
      }
      this._cdr.detectChanges();
    }
  }

  selectStep(index: number): void {
    this.selectedStep = index;
    this._cdr.detectChanges();
  }

  submitForm() {
    this.formSubmitted = true;

    const isInvalid =
      !this.campaignName.trim() ||
      !this.selectedDays.length ||
      !this.selectedEmail ||
      !this.startHour ||
      !this.endHour ||
      this.minTimeBetweenEmails <= 0 ||
      this.maxLeadsPerDay <= 0 ||
      !this.emailSequence.some(email => email.subject.trim() && email.body.trim());

    if (isInvalid) {
      alert('Please fill in all required fields including at least one email sequence');
      return;
    }

    const validEmailSequences = this.emailSequence.filter(
      email => email.subject.trim() && email.body.trim()
    );
  
    this._emailService.emailOutreach(new SmartleadAIDto({
      campaignName: this.campaignName,
      days_of_the_week: this.selectedDays,
      start_hour: this.startHour,
      end_hour: this.endHour,
      min_time_btw_emails: this.minTimeBetweenEmails,
      max_new_leads_per_day: this.maxLeadsPerDay,
      emailId: this.selectedEmail,
      emailInputs: validEmailSequences.map((email, index) => new EmailSequenceInput({
        sequenceNumber: index + 1,
        delayInDays: index * 1,
        subject: email.subject.trim(),
        body: email.body.trim(),
        variantLabel: `Variant ${index + 1}`
      }))
    })).subscribe({
      next: (response) => {
        console.log('API success:', response);
        alert('Email Campaign created successfully!');
        this._modalService.hide();
      },
      error: (err) => {
        console.error('API error:', err);
        alert('Failed to create campaign: ' + (err.message || 'Unknown error'));
      }
    });
  }

  // Updated variable insertion methods
  toggleVariablesMenu(): void {
    this.isVariablesMenuOpen = !this.isVariablesMenuOpen;
    if (this.isVariablesMenuOpen) {
      // Store currently focused element
      this.focusedElement = document.activeElement as HTMLInputElement | HTMLTextAreaElement;
    }
  }

  insertVariable(variableCode: string): void {
    const element = this.focusedElement;
    
    if (element && (element.id === 'emailSubject' || element.id === 'emailBody')) {
      const cursorPosition = element.selectionStart || 0;
      const currentValue = element.value;
      const newValue = currentValue.slice(0, cursorPosition) + 
                      variableCode + 
                      currentValue.slice(cursorPosition);
      
      if (element.id === 'emailSubject') {
        this.emailSequence[this.selectedStep].subject = newValue;
      } else {
        this.emailSequence[this.selectedStep].body = newValue;
      }
      
      // Restore focus and cursor position
      setTimeout(() => {
        element.focus();
        element.selectionStart = element.selectionEnd = cursorPosition + variableCode.length;
      }, 0);
    }
    
    this.isVariablesMenuOpen = false;
    this._cdr.detectChanges();
  }

  // Updated drag and drop methods
  onDragStart(event: DragEvent, variableCode: string) {
    event.dataTransfer?.setData('text/plain', variableCode);
    const dragIcon = document.createElement('div');
    dragIcon.classList.add('drag-icon');
    dragIcon.textContent = variableCode;
    document.body.appendChild(dragIcon);
    event.dataTransfer?.setDragImage(dragIcon, 0, 0);
    setTimeout(() => document.body.removeChild(dragIcon), 0);
  }

  onDragOver(event: DragEvent) {
    event.preventDefault();
    event.stopPropagation();
    const target = event.target as HTMLElement;
    target.classList.add('drag-over');
  }

  onDragLeave(event: DragEvent) {
    event.preventDefault();
    event.stopPropagation();
    const target = event.target as HTMLElement;
    target.classList.remove('drag-over');
  }

  onDrop(event: DragEvent, field: 'subject' | 'body') {
    event.preventDefault();
    event.stopPropagation();
    
    const target = event.target as HTMLElement;
    target.classList.remove('drag-over');

    const variableCode = event.dataTransfer?.getData('text/plain');
    if (!variableCode) return;

    const element = event.target as HTMLTextAreaElement | HTMLInputElement;
    const cursorPosition = element.selectionStart || 0;
    
    if (field === 'subject') {
      const currentValue = this.emailSequence[this.selectedStep].subject;
      this.emailSequence[this.selectedStep].subject = 
        currentValue.slice(0, cursorPosition) + 
        variableCode + 
        currentValue.slice(cursorPosition);
    } else {
      const currentValue = this.emailSequence[this.selectedStep].body;
      this.emailSequence[this.selectedStep].body = 
        currentValue.slice(0, cursorPosition) + 
        variableCode + 
        currentValue.slice(cursorPosition);
    }

    this._cdr.detectChanges();
  }

toggleVariables() {
  this.showVariables = !this.showVariables;
}

}
