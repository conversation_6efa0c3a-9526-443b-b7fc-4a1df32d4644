import { ChangeDetectorRef, Component, Injector, OnInit } from '@angular/core';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { CreateCampaignComponent } from '../create-campaign/create-campaign.component';
import { EmailsAutomationServiceProxy } from '@shared/service-proxies/service-proxies';
import { EditCampaignComponent } from '../edit-campaign/edit-campaign.component';

@Component({
  selector: 'app-email-campaign',
  templateUrl: './email-campaign.component.html',
  styleUrl: './email-campaign.component.css'
})
export class EmailCampaignComponent implements OnInit {

  campaigns: any[] = [];
  isLoading: boolean = false;
  showCreateCampaignModal = false;

  constructor(
    injector: Injector,
    private _modalService: BsModalService,
    protected _cdr: ChangeDetectorRef,
    private _emailService: EmailsAutomationServiceProxy,
  ) {}

  ngOnInit(): void {
    this.GetAllCampaigns();
  }

  GetAllCampaigns() {
    this.isLoading = true;
    this._emailService.getAllCampaignAnalytics().subscribe({
      next: res => {
        console.log("API Response:", res);
        this.campaigns = res || [];
        this.isLoading = false;
        this._cdr.detectChanges();
      },
      error: err => {
        console.error("API Error:", err);
        this.isLoading = false;
      }
    });
  }

  deleteCampaign(campaignId: string) {
    if (confirm('Are you sure you want to delete this campaign?')) {
      this._emailService.deleteCampaign(campaignId).subscribe({
        next: () => {
          this.campaigns = this.campaigns.filter(c => c.id !== campaignId);
          this._cdr.detectChanges();
        },
        error: (err) => {
          console.error('Delete failed:', err);
          alert('Failed to delete campaign.');
        }
      });
    }
  }

  getProgress(campaign: any): number {
    if (!campaign.campaign_lead_stats?.total || campaign.campaign_lead_stats.total === 0) {
      return 0;
    }
    return Math.round((campaign.campaign_lead_stats.completed / campaign.campaign_lead_stats.total) * 100);
  }

  openModal() {
     try {
          const Dialog = this._modalService.show(CreateCampaignComponent, {
            class: 'modal-lg',
          });
        } catch (error) {
          console.error('Error opening create lead modal:', error);
        }
  }

  closeModal() {
    this.showCreateCampaignModal = false;
  }

  editCampaign(id: any) {
    try {
      const dialog = this._modalService.show(EditCampaignComponent, {
        class: 'modal-lg',
        initialState: {
          id: id
        }
      });
    } catch (error) {
      console.error('Error opening edit campaign modal:', error);
    }
  }
}


