{"name": "AISDR", "version": "4.7.1", "private": true, "license": "MIT", "scripts": {"pree2e": "webdriver-manager update --standalone false --gecko false", "e2e": "protractor", "hmr": "ng serve --host 0.0.0.0 --port 4200 --hmr", "lint": "tslint --force --project src/tsconfig.json src/**/*.ts -t verbose", "ng": "ng", "start": "ng serve --host 0.0.0.0 --port 4200", "test": "ng test"}, "dependencies": {"@angular/animations": "^18.1.2", "@angular/common": "^18.1.2", "@angular/compiler": "^18.1.2", "@angular/core": "^18.1.2", "@angular/forms": "^18.1.2", "@angular/platform-browser": "^18.1.2", "@angular/platform-browser-dynamic": "^18.1.2", "@angular/router": "^18.1.2", "@fortawesome/fontawesome-free": "^6.6.0", "@microsoft/signalr": "^8.0.7", "abp-ng2-module": "10.0.0", "abp-web-resources": "^5.9.1", "admin-lte-css-only": "^3.2.0", "bootstrap": "^5.3.5", "core-js": "^3.37.1", "famfamfam-flags": "^1.0.0", "lodash-es": "^4.17.21", "moment": "2.30.1", "moment-timezone": "0.5.45", "ngx-bootstrap": "^18.1.3", "ngx-pagination": "^6.0.3", "push.js": "1.0.12", "rxjs": "^7.8.1", "sweetalert2": "^11.12.2", "ts-helpers": "^1.1.2", "tslib": "^2.6.3"}, "devDependencies": {"@angular-devkit/build-angular": "^18.1.2", "@angular/cli": "^18.1.2", "@angular/compiler-cli": "^18.1.2", "@angularclass/hmr": "^3.0.0", "@types/jasmine": "~5.1.4", "@types/lodash-es": "^4.17.12", "@types/moment-timezone": "^0.5.30", "@types/node": "^20.11.1", "jasmine-core": "~5.1.2", "jasmine-spec-reporter": "~7.0.0", "karma": "~6.4.3", "karma-chrome-launcher": "~3.2.0", "karma-cli": "^2.0.0", "karma-coverage-istanbul-reporter": "~3.0.3", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "^2.1.0", "nswag": "^14.1.0", "protractor": "~7.0.0", "ts-node": "^10.9.2", "tslint": "~6.1.3", "typescript": "5.5.3", "webpack-bundle-analyzer": "^4.10.2"}, "angular-cli": {}}