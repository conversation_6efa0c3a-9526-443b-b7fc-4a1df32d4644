import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { HttpClientJsonpModule } from '@angular/common/http';
import { HttpClientModule } from '@angular/common/http';
import { ModalModule } from 'ngx-bootstrap/modal';
import { BsDropdownModule } from 'ngx-bootstrap/dropdown';
import { CollapseModule } from 'ngx-bootstrap/collapse';
import { TabsModule } from 'ngx-bootstrap/tabs';
import { NgxPaginationModule } from 'ngx-pagination';
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { SharedModule } from '@shared/shared.module';
import { ServiceProxyModule } from '@shared/service-proxies/service-proxy.module';
// layout
import { HeaderComponent } from './layout/header.component';
import { HeaderLeftNavbarComponent } from './layout/header-left-navbar.component';
import { HeaderLanguageMenuComponent } from './layout/header-language-menu.component';
import { HeaderUserMenuComponent } from './layout/header-user-menu.component';
import { FooterComponent } from './layout/footer.component';
import { SidebarComponent } from './layout/sidebar.component';
import { SidebarLogoComponent } from './layout/sidebar-logo.component';
import { SidebarUserPanelComponent } from './layout/sidebar-user-panel.component';
import { SidebarMenuComponent } from './layout/sidebar-menu.component';
import { PeopleLeadsComponent } from './Leads/people-leads/people-leads.component';
import { CreateLeadComponent } from './Leads/create-lead/create-lead.component';
import { EmailCampaignComponent } from './OutreachAutomation/email-campaign/email-campaign.component';
import { CreateCampaignComponent } from './OutreachAutomation/create-campaign/create-campaign.component';
import { CampaignLeadsComponent } from './OutreachAutomation/campaign-leads/campaign-leads.component';
import { EmailAccountsComponent } from './OutreachAutomation/email-accounts/email-accounts.component';
import { AddEmailAccountComponent } from './OutreachAutomation/add-email-account/add-email-account.component';
import { EditCampaignComponent } from './OutreachAutomation/edit-campaign/edit-campaign.component';

@NgModule({
    declarations: [
        AppComponent,
        // layout
        HeaderComponent,
        HeaderLeftNavbarComponent,
        HeaderLanguageMenuComponent,
        HeaderUserMenuComponent,
        FooterComponent,
        SidebarComponent,
        SidebarLogoComponent,
        SidebarUserPanelComponent,
        SidebarMenuComponent,
        PeopleLeadsComponent,
        EmailCampaignComponent,
        CreateCampaignComponent,
        CampaignLeadsComponent,
        EmailAccountsComponent,
        AddEmailAccountComponent,
        EditCampaignComponent,
    ],
    imports: [
        AppRoutingModule,
        CommonModule,
        FormsModule,
        ReactiveFormsModule,
        HttpClientModule,
        HttpClientJsonpModule,
        ModalModule.forChild(),
        BsDropdownModule,
        CollapseModule,
        TabsModule,
        ServiceProxyModule,
        NgxPaginationModule,
        SharedModule,
        CreateLeadComponent 
    ],
    providers: []
})
export class AppModule {}
