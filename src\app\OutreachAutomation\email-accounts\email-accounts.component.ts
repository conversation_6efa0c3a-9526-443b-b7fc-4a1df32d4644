import { ChangeDetectorRef, Component, Injector, OnInit } from '@angular/core';
import { BsModalService, BsModalRef } from 'ngx-bootstrap/modal';
import { EmailsAutomationServiceProxy } from '@shared/service-proxies/service-proxies';
import { AddEmailAccountComponent } from '../add-email-account/add-email-account.component';

@Component({
  selector: 'app-email-accounts',
  templateUrl: './email-accounts.component.html',
  styleUrl: './email-accounts.component.css'
})
export class EmailAccountsComponent implements OnInit {

  emailAccounts: any[] = [];

  constructor(
      injector: Injector,
      private _modalService: BsModalService,
      protected _cdr: ChangeDetectorRef,
      private _emailService: EmailsAutomationServiceProxy,
    ) {}

    ngOnInit(): void {
      this.GetALlEmailAccounts();
    }

   openModal() {
       try {
            const Dialog = this._modalService.show(AddEmailAccountComponent, {
              class: 'modal-lg',
            });
          } catch (error) {
            console.error('Error opening create lead modal:', error);
          }
    }

    GetALlEmailAccounts() {
      this._emailService.getEmailAccounts(0, 10).subscribe({
        next: (res) => {
          this.emailAccounts = res || [];
          console.log("API Response:", this.emailAccounts);
          this._cdr.detectChanges();
        },
        error: (err) => {
          console.error("Error fetching email accounts:", err);
        }
      });
    }

    getIcon(type: string): string {
      switch (type?.toLowerCase()) {
        case 'smtp':
          return '📤 SMTP';
        case 'gmail':
          return '📧 Gmail';
        case 'outlook':
          return '📬 Outlook';
        default:
          return type || 'N/A';
      }
    }
  
    getReputationLabel(reputation: string | null | undefined): string {
      if (!reputation || reputation === '0%') return 'NA';
      return reputation;
    }
  
    getWarmupStatus(status: string | null | undefined): string {
      return status === 'ACTIVE' ? 'Yes' : 'No';
    }
  
}
