import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';
import { AppRouteGuard } from '@shared/auth/auth-route-guard';
import { AppComponent } from './app.component';
import { prependOnceListener } from 'process';
import { PeopleLeadsComponent } from './Leads/people-leads/people-leads.component';
import { EmailCampaignComponent } from './OutreachAutomation/email-campaign/email-campaign.component';
import { CampaignLeadsComponent } from './OutreachAutomation/campaign-leads/campaign-leads.component';
import { EmailAccountsComponent } from './OutreachAutomation/email-accounts/email-accounts.component';

@NgModule({
    imports: [
        RouterModule.forChild([
            {
                path: '',
                component: AppComponent,
                children: [
                    {
                        path: 'home',
                        loadChildren: () => import('./home/<USER>').then((m) => m.HomeModule),
                        canActivate: [AppRouteGuard]
                    },
                    {
                        path: 'leads/people-leads', // Fixed route to match sidebar
                        component: PeopleLeadsComponent,
                        canActivate: [AppRouteGuard]
                    },
                    {
                        path: 'OutreachAutomation/email-campaign', // Fixed route to match sidebar
                        component: EmailCampaignComponent,
                        canActivate: [AppRouteGuard]
                    },
                    {
                    path: 'OutreachAutomation/campaign-leads', 
                    component: CampaignLeadsComponent,
                    canActivate: [AppRouteGuard]
                    },
                    {
                        path: 'OutreachAutomation/email-accounts', 
                        component: EmailAccountsComponent,
                        canActivate: [AppRouteGuard]
                        },
                    {
                        path: 'about',
                        loadChildren: () => import('./about/about.module').then((m) => m.AboutModule),
                        canActivate: [AppRouteGuard]
                    },
                    {
                        path: 'users',
                        loadChildren: () => import('./users/users.module').then((m) => m.UsersModule),
                        data: { permission: 'Pages.Users' },
                        canActivate: [AppRouteGuard]
                    },
                    {
                        path: 'roles',
                        loadChildren: () => import('./roles/roles.module').then((m) => m.RolesModule),
                        data: { permission: 'Pages.Roles' },
                        canActivate: [AppRouteGuard]
                    },
                    {
                        path: 'tenants',
                        loadChildren: () => import('./tenants/tenants.module').then((m) => m.TenantsModule),
                        data: { permission: 'Pages.Tenants' },
                        canActivate: [AppRouteGuard]
                    },
                    {
                        path: 'update-password',
                        loadChildren: () => import('./users/users.module').then((m) => m.UsersModule),
                        canActivate: [AppRouteGuard]
                    },
                ]
            }
        ])
    ],
    exports: [RouterModule]
})
export class AppRoutingModule { }
