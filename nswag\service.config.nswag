{"runtime": "<PERSON><PERSON><PERSON>", "defaultVariables": null, "documentGenerator": {"fromDocument": {"url": "https://localhost:44311/swagger/v1/swagger.json", "output": null, "newLineBehavior": "Auto"}}, "codeGenerators": {"openApiToTypeScriptClient": {"className": "{controller}ServiceProxy", "moduleName": "", "namespace": "", "typeScriptVersion": 2.0, "template": "Angular", "promiseType": "Promise", "httpClass": "HttpClient", "withCredentials": false, "useSingletonProvider": false, "injectionTokenType": "InjectionToken", "rxJsVersion": 7.0, "dateTimeType": "MomentJS", "nullValue": "Undefined", "generateClientClasses": true, "generateClientInterfaces": false, "generateOptionalParameters": false, "exportTypes": true, "wrapDtoExceptions": false, "exceptionClass": "ApiException", "clientBaseClass": null, "wrapResponses": false, "wrapResponseMethods": [], "generateResponseClasses": true, "responseClass": "SwaggerResponse", "protectedMethods": [], "configurationClass": null, "useTransformOptionsMethod": false, "useTransformResultMethod": false, "generateDtoTypes": true, "operationGenerationMode": "MultipleClientsFromPathSegments", "markOptionalProperties": false, "generateCloneMethod": true, "typeStyle": "Class", "enumStyle": "Enum", "useLeafType": false, "classTypes": [], "extendedClasses": [], "extensionCode": null, "generateDefaultValues": true, "excludedTypeNames": [], "excludedParameterNames": [], "handleReferences": false, "generateTypeCheckFunctions": false, "generateConstructorInterface": true, "convertConstructorInterfaceData": false, "importRequiredTypes": true, "useGetBaseUrlMethod": false, "baseUrlTokenName": "API_BASE_URL", "queryNullValue": "", "useAbortSignal": false, "inlineNamedDictionaries": false, "inlineNamedAny": false, "includeHttpContext": false, "templateDirectory": null, "typeNameGeneratorType": null, "propertyNameGeneratorType": null, "enumNameGeneratorType": null, "serviceHost": null, "serviceSchemes": null, "output": "../src/shared/service-proxies/service-proxies.ts", "newLineBehavior": "Auto"}, "openApiToCSharpClient": {"clientBaseClass": null, "configurationClass": null, "generateClientClasses": true, "generateClientInterfaces": false, "clientBaseInterface": null, "injectHttpClient": false, "disposeHttpClient": true, "protectedMethods": [], "generateExceptionClasses": true, "exceptionClass": "SwaggerException", "wrapDtoExceptions": true, "useHttpClientCreationMethod": false, "httpClientType": "System.Net.Http.HttpClient", "useHttpRequestMessageCreationMethod": false, "useBaseUrl": true, "generateBaseUrlProperty": true, "generateSyncMethods": false, "generatePrepareRequestAndProcessResponseAsAsyncMethods": false, "exposeJsonSerializerSettings": false, "clientClassAccessModifier": "public", "typeAccessModifier": "public", "generateContractsOutput": false, "contractsNamespace": null, "contractsOutputFilePath": null, "parameterDateTimeFormat": "s", "parameterDateFormat": "yyyy-MM-dd", "generateUpdateJsonSerializerSettingsMethod": true, "useRequestAndResponseSerializationSettings": false, "serializeTypeInformation": false, "queryNullValue": "", "className": "{controller}Client", "operationGenerationMode": "MultipleClientsFromOperationId", "additionalNamespaceUsages": [], "additionalContractNamespaceUsages": [], "generateOptionalParameters": false, "generateJsonMethods": true, "enforceFlagEnums": false, "parameterArrayType": "System.Collections.Generic.IEnumerable", "parameterDictionaryType": "System.Collections.Generic.IDictionary", "responseArrayType": "System.Collections.ObjectModel.ObservableCollection", "responseDictionaryType": "System.Collections.Generic.Dictionary", "wrapResponses": false, "wrapResponseMethods": [], "generateResponseClasses": true, "responseClass": "SwaggerResponse", "namespace": "MyNamespace", "requiredPropertiesMustBeDefined": true, "dateType": "System.DateTime", "jsonConverters": null, "anyType": "object", "dateTimeType": "System.DateTime", "timeType": "System.TimeSpan", "timeSpanType": "System.TimeSpan", "arrayType": "System.Collections.ObjectModel.ObservableCollection", "arrayInstanceType": "System.Collections.ObjectModel.Collection", "dictionaryType": "System.Collections.Generic.Dictionary", "dictionaryInstanceType": "System.Collections.Generic.Dictionary", "arrayBaseType": "System.Collections.ObjectModel.ObservableCollection", "dictionaryBaseType": "System.Collections.Generic.Dictionary", "classStyle": "Inpc", "jsonLibrary": "NewtonsoftJson", "generateDefaultValues": true, "generateDataAnnotations": true, "excludedTypeNames": [], "excludedParameterNames": [], "handleReferences": false, "generateImmutableArrayProperties": false, "generateImmutableDictionaryProperties": false, "jsonSerializerSettingsTransformationMethod": null, "inlineNamedArrays": false, "inlineNamedDictionaries": false, "inlineNamedTuples": true, "inlineNamedAny": false, "generateDtoTypes": true, "generateOptionalPropertiesAsNullable": false, "generateNullableReferenceTypes": false, "templateDirectory": null, "typeNameGeneratorType": null, "propertyNameGeneratorType": null, "enumNameGeneratorType": null, "serviceHost": null, "serviceSchemes": null, "output": null, "newLineBehavior": "Auto"}, "openApiToCSharpController": {"controllerBaseClass": null, "controllerStyle": "Partial", "controllerTarget": "AspNet", "useCancellationToken": false, "useActionResultType": false, "generateModelValidationAttributes": false, "routeNamingStrategy": "None", "basePath": null, "className": "{controller}", "operationGenerationMode": "MultipleClientsFromOperationId", "additionalNamespaceUsages": ["System.Web.Http"], "additionalContractNamespaceUsages": [], "generateOptionalParameters": false, "generateJsonMethods": true, "enforceFlagEnums": false, "parameterArrayType": "System.Collections.Generic.IEnumerable", "parameterDictionaryType": "System.Collections.Generic.IDictionary", "responseArrayType": "System.Collections.ObjectModel.ObservableCollection", "responseDictionaryType": "System.Collections.Generic.Dictionary", "wrapResponses": false, "wrapResponseMethods": [], "generateResponseClasses": true, "responseClass": "SwaggerResponse", "namespace": "MyNamespace", "requiredPropertiesMustBeDefined": true, "dateType": "System.DateTime", "jsonConverters": null, "anyType": "object", "dateTimeType": "System.DateTime", "timeType": "System.TimeSpan", "timeSpanType": "System.TimeSpan", "arrayType": "System.Collections.Generic.IEnumerable", "arrayInstanceType": "System.Collections.ObjectModel.Collection", "dictionaryType": "System.Collections.Generic.Dictionary", "dictionaryInstanceType": "System.Collections.Generic.Dictionary", "arrayBaseType": "System.Collections.ObjectModel.ObservableCollection", "dictionaryBaseType": "System.Collections.Generic.Dictionary", "classStyle": "Inpc", "jsonLibrary": "NewtonsoftJson", "generateDefaultValues": true, "generateDataAnnotations": true, "excludedTypeNames": [], "excludedParameterNames": [], "handleReferences": false, "generateImmutableArrayProperties": false, "generateImmutableDictionaryProperties": false, "jsonSerializerSettingsTransformationMethod": null, "inlineNamedArrays": false, "inlineNamedDictionaries": false, "inlineNamedTuples": true, "inlineNamedAny": false, "generateDtoTypes": true, "generateOptionalPropertiesAsNullable": false, "generateNullableReferenceTypes": false, "templateDirectory": null, "typeNameGeneratorType": null, "propertyNameGeneratorType": null, "enumNameGeneratorType": null, "serviceHost": null, "serviceSchemes": null, "output": null, "newLineBehavior": "Auto"}}}