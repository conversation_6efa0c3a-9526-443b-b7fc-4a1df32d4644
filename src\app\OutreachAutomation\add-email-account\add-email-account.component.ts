import { ChangeDetectorRef, Component } from '@angular/core';
import { EmailsAutomationServiceProxy, SaveEmailAccountDto } from '@shared/service-proxies/service-proxies';
import { BsModalService } from 'ngx-bootstrap/modal';

@Component({
  selector: 'app-add-email-account',
  templateUrl: './add-email-account.component.html',
  styleUrl: './add-email-account.component.css'
})
export class AddEmailAccountComponent {
  constructor(
    protected _cdr: ChangeDetectorRef,
    private _emailService: EmailsAutomationServiceProxy,
    private _modalService: BsModalService
  ) {}

  form = {
    from_name: '',
    from_email: '',
    user_name: '',
    password: '',
    smtp_host: '',
    smtp_port: 465,
    imap_host: '',
    imap_port: 993,
    max_email_per_day: 50,
    custom_tracking_url: '',
    bcc: '',
    signature: '',
    warmup_enabled: false,
    total_warmup_per_day: null,
    daily_rampup: null,
    reply_rate_percentage: null,
    client_id: null
  };

  onSubmit() {
    const input: any = {
      id: 0,
      fromName: this.form.from_name,
      fromEmail: this.form.from_email,
      userName: this.form.user_name,
      password: this.form.password,
      smtpHost: this.form.smtp_host,
      smtpPort: this.form.smtp_port,
      imapHost: this.form.imap_host,
      imapPort: this.form.imap_port,
      maxEmailPerDay: this.form.max_email_per_day,
      customTrackingUrl: this.form.custom_tracking_url,
      bcc: this.form.bcc,
      signature: this.form.signature,
      warmupEnabled: this.form.warmup_enabled,
      totalWarmupPerDay: this.form.total_warmup_per_day,
      dailyRampup: this.form.daily_rampup,
      replyRatePercentage: this.form.reply_rate_percentage,
      clientId: this.form.client_id,
    };
     console.log("Input",input);
    this._emailService.saveEmailAccount(input).subscribe({
      next: () => {
        alert('Email account added successfully!');
        this._modalService.hide();
        this._cdr.detectChanges();
      },
      error: (err) => {
        console.error('Error saving email account:', err);
        alert('Failed to save email account');
      }
    });
  }

}
