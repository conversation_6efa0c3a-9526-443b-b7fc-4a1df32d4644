import { ChangeDetectorRef, Component, Injector } from '@angular/core';
import { CommonModule } from '@node_modules/@angular/common';
import { FormsModule } from '@node_modules/@angular/forms';
import { BsModalService } from 'ngx-bootstrap/modal';
import { LeadAppServicesServiceProxy } from '@shared/service-proxies/service-proxies';

@Component({
  selector: 'app-create-lead',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './create-lead.component.html',
  styleUrl: './create-lead.component.css'
})
export class CreateLeadComponent {

  leadData : any = {
    getPersonalEmails: false,
    getWorkEmails: false,
    totalRecords: 500,
    url: ''
  };

   constructor(
      injector: Injector,
      private _modalService: BsModalService,
      protected _cdr: ChangeDetectorRef,
      private _leadService: LeadAppServicesServiceProxy,
    ) {
      // Remove super call since the class doesn't extend anything
    }

    closeModal(): void {
      this._modalService.hide();
    }

    onSubmit(): void {
      if (!this.leadData.url) {
        alert('Please enter a URL');
        return;
      }

      if (this.leadData.totalRecords < 500) {
        alert('Total records are less than the minimum allowed (500). Cancelling the request');
        return;
      }
  
      this._leadService.runApifyActor(this.leadData).subscribe({
        next: (response) => {
          console.log('API success:', response);
          alert('Lead submitted successfully!');
          this._modalService.hide(); // close modal
        },
        error: (err) => {
          console.error('API error:', err);
          alert('Failed to submit lead.');
        }
      });
    }
}
