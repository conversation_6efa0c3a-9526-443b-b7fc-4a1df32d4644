<div>
    <section class="content-header">
      <div class="container-fluid">
        <div class="row">
          <div class="col-12">
            <h1>{{ "People" | localize }}</h1>
          </div>
        </div>
      </div>
    </section>
  
    <section class="content px-2">
      <div class="container-fluid">
        <div class="card">
          <div class="card-body">
            <div class="text-right ">
              <button class="btn btn-primary" (click)="openCreateModal()">
                <i class="fa fa-plus-square"></i> {{ "Create" | localize }}
              </button>
            </div>
          </div>
        </div>
  
        <div class="card">
          <div class="table-responsive table-container">
            <table class="table table-striped mt-2 custome_table">
              <thead class="bg-light table-header">
                <tr>
                  <th>Sr No.</th>
                  <th>Full Name</th>
                  <th>Email</th>
                  <th>Number</th>
                  <th>Job Title</th>
                  <th>Company Name</th>
                  <th>State</th>
                  <th>City</th>
                  <th>Country</th>
                  <th>Industry</th>
                  <th>Status</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let item of displayedLeads; let i = index">
                  <td>{{ (currentPage - 1) * itemsPerPage + i + 1 }}</td>
                  <td>{{ item.fullName }}</td>
                  <td>{{ item.email }}</td>
                  <td>{{ item.contactNumber }}</td>
                  <td>{{ item.jobTitle }}</td>
                  <td>{{ item.companyName }}</td>
                  <td>{{ item.state }}</td>
                  <td>{{ item.city }}</td>
                  <td>{{ item.country }}</td>
                  <td>{{ item.industry }}</td>
                  <td>{{ item.email_Status }}</td>
                  <td class="text-center">
                    <!-- Actions (Edit, Delete, etc.) -->
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
  
          <!-- Pagination Controls -->
          <ul style="overflow:auto;" *ngIf="totalPages > 1" class="pagination mt-3">
            <li class="page-item" [class.disabled]="currentPage === 1">
              <a class="page-link" (click)="changePage(currentPage - 1)">Previous</a>
            </li>
  
            <li *ngFor="let page of getPaginationNumbers()" class="page-item" [class.active]="page === currentPage">
              <a class="page-link" (click)="changePage(page)">{{ page }}</a>
            </li>
  
            <li class="page-item" [class.disabled]="currentPage === totalPages">
              <a class="page-link" (click)="changePage(currentPage + 1)">Next</a>
            </li>
          </ul>
        </div>
      </div>
    </section>
  </div>
  